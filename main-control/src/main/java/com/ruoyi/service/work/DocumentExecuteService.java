package com.ruoyi.service.work;

import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.domain.basicData.*;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.basicData.DocumentInventoryDetailService;
import com.ruoyi.service.erp.ErpReportService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.ResultMsg;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.DocumentDetailVo;
import com.ruoyi.vo.document.DocumentInventoryConfirmVo;
import com.ruoyi.vo.document.DocumentInventoryItemVo;
import com.ruoyi.vo.lk.LkSendTaskDetail;
import com.ruoyi.vo.lk.LkSendTaskRequest;
import com.ruoyi.vo.lk.LkTaskMaterial;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DocumentExecuteService {
    @Resource
    BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    BasicDocumentDetailMapper basicDocumentDetailMapper;

    @Resource
    DocumentInventoryDetailService documentInventoryDetailService;

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    @Resource
    BasicMaterialBatchInventoryMapper materialBatchInventoryMapper;

    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    @Resource
    LkSystemService lkSystemService;

    @Resource
    MesUpperService mesUpperService;

    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    @Resource
    ErpReportService erpReportService;

    @Resource
    BasicDocumentInfoService basicDocumentInfoService;


    /**
     * 单据出入库
     * description: 确认本次出入库的实际数量
     *
     * @param param 出入库确认请求
     * @return 操作结果
     * @author: sqpeng
     */
    @Transactional
    public ResponseResult documentInventoryConfirm(DocumentInventoryConfirmVo param) {
        Integer type = param.getType();
        List<DocumentInventoryItemVo> items = param.getItems();
        // 验证数据有效性
        ResponseResult validationResult = validateConfirmItems(items, type);
        if (!validationResult.getCode().equals(ResultMsg.successCode)) {
            return validationResult;
        }
        // 按容器类型分组处理
        List<DocumentInventoryItemVo> boxLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> plateLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> profileLkItems = new ArrayList<>();
        List<DocumentInventoryItemVo> withoutLkItems = new ArrayList<>();
        List<RecordMaterialInout> inOutList = new ArrayList<>();
        for (DocumentInventoryItemVo item : items) {
            // 获取批次记录
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            if (detail == null) {
                return ResponseResult.getErrorResult("批次记录不存在，ID: " + item.getDocumentInventoryDetailId());
            }
            // 获取容器编码
            String containerCode = getContainerCodeForConfirm(item, detail, type);
            if (containerCode == null) {
                return ResponseResult.getErrorResult("无法获取容器编码，批次ID: " + item.getDocumentInventoryDetailId());
            }
            // 按容器类型分类
            if (containerCode.contains(CommonConstant.LkName.BOX)) {
                boxLkItems.add(item);
            } else if (containerCode.contains(CommonConstant.LkName.PLATE)) {
                plateLkItems.add(item);
            } else if (containerCode.contains(CommonConstant.LkName.PROFILE)) {
                profileLkItems.add(item);
            } else if (!containerCode.contains("立库")) {
                withoutLkItems.add(item);
            }

            // 创建出入库记录
            createInOutRecord(item, detail, containerCode, inOutList);
        }

        try {
            // 分别处理不同类型的容器
            //非立库容器
            if (!withoutLkItems.isEmpty()) {
                executeWithoutLkConfirm(withoutLkItems, type);
            }
            //料框立库
            if (!boxLkItems.isEmpty()) {
                executeLkConfirm(boxLkItems, CommonConstant.LkName.BOX, type);
            }
            //板材立库
            if (!plateLkItems.isEmpty()) {
                executeLkConfirm(plateLkItems, CommonConstant.LkName.PLATE, type);
            }
            //型材立库
            if (!profileLkItems.isEmpty()) {
                executeLkConfirm(profileLkItems, CommonConstant.LkName.PROFILE, type);
            }

            // 保存出入库记录
            recordMaterialInoutService.addBatchInout(inOutList);
            // 更新单据状态
            updateDocumentStatusByItems(items);
            // 上报ERP
            reportToErpByItems(items, type);
            return ResponseResult.getSuccessResult();
        } catch (CustomException e) {
            log.info("单据出入库确认失败：{}", e.getMessage());
            return ResponseResult.getErrorResult(e.getMessage());
        } catch (Exception e) {
            log.info("单据出入库确认发生未知异常：{}", e.getMessage(), e);
            return ResponseResult.getErrorResult("出入库操作失败，请联系系统管理员");
        }
    }

    /**
     * 生成立库任务号
     */
    public String getLkTaskNo(String baseCode) {
        // 1. 获取当前时间戳（精确到毫秒）
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 3. 组合生成任务号
        return String.format("%s-%s", baseCode, timestamp);
    }

    /**
     * 获取确认操作的容器编码
     */
    private String getContainerCodeForConfirm(DocumentInventoryItemVo item, DocumentInventoryDetail detail, Integer type) {
        if (type == CommonConstant.InoutType.IN) {
            // 入库：使用前端传递的容器编码
            return item.getContainerCode();
        } else {
            // 出库：从数据库记录中获取容器编码
            return detail.getContainerCode();
        }
    }

    /**
     * 验证出入库项数据
     */
    private ResponseResult validateConfirmItems(List<DocumentInventoryItemVo> items, Integer type) {
        // 按DocumentInventoryDetailId分组，统计每个批次的出入总量
        Map<String, List<DocumentInventoryItemVo>> groupedItems = items.stream()
                .collect(Collectors.groupingBy(DocumentInventoryItemVo::getDocumentInventoryDetailId));
        for (Map.Entry<String, List<DocumentInventoryItemVo>> entry : groupedItems.entrySet()) {
            String detailId = entry.getKey();
            List<DocumentInventoryItemVo> itemsForDetail = entry.getValue();
            // 获取批次记录
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(detailId);
            if (detail == null) {
                return ResponseResult.getErrorResult("批次记录不存在，ID: " + detailId);
            }
            // 校验质检状态
            if (!isQcStatusValid(detail.getQcStatus(), detail.getIsConcession())) {
                return ResponseResult.getErrorResult("批次ID " + detailId +
                        " 质检状态不允许出入库操作，当前状态：" + getQcStatusDescription(detail.getQcStatus()) +
                        (detail.getIsConcession() != null && detail.getIsConcession().equals(CommonConstant.IsConcession.YES) ? "（已让步接收）" : ""));
            }
            // 校验出入库状态
            if (!isWarehouseStatusValid(detail.getWarehouseStatus())) {
                return ResponseResult.getErrorResult("批次ID " + detailId + " 出入库状态不允许操作，当前状态：" + getWarehouseStatusDescription(detail.getWarehouseStatus()));
            }
            // 计算该批次的出入总量
            Integer totalConfirmQuantity = itemsForDetail.stream()
                    .mapToInt(DocumentInventoryItemVo::getConfirmQuantity)
                    .sum();

            Integer remainingQuantity = detail.getQuantity() - detail.getCompletedNum();
            // 验证出入总量不能超过剩余可操作数量
            if (totalConfirmQuantity > remainingQuantity) {
                return ResponseResult.getErrorResult("批次ID " + detailId + " 的出入总量 " + totalConfirmQuantity + " 超过了剩余可操作数量 " + remainingQuantity +
                        "（计划数量: " + detail.getQuantity() + "，已完成数量: " + detail.getCompletedNum() + "）");
            }

            // 验证每个出入项的数量必须大于0，并验证容器编码
            for (DocumentInventoryItemVo item : itemsForDetail) {
                if (item.getConfirmQuantity() <= 0) {
                    return ResponseResult.getErrorResult("批次ID " + detailId + " 的确认数量必须大于0，当前值: " + item.getConfirmQuantity());
                }

                // 入库时验证容器编码
                if (type == CommonConstant.InoutType.IN && (item.getContainerCode() == null || item.getContainerCode().trim().isEmpty())) {
                    return ResponseResult.getErrorResult("入库操作必须指定目标容器编码");
                }
            }
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 创建出入库记录
     */
    private void createInOutRecord(DocumentInventoryItemVo item, DocumentInventoryDetail detail, String containerCode, List<RecordMaterialInout> inOutList) {
        // 获取单据信息
        BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
        BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentDetail.getDocumentCode());
        RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
        recordMaterialInout.setId(UUID.randomUUID().toString());
        recordMaterialInout.setInoutType(documentInfo.getTransactionType());
        recordMaterialInout.setDataOrigin(documentInfo.getBusinessSource());
        recordMaterialInout.setBoundType(documentInfo.getBusinessType());
        recordMaterialInout.setMaterialCode(detail.getMaterialCode());
        recordMaterialInout.setTotalNum(item.getConfirmQuantity());
        recordMaterialInout.setBoundIndex(documentInfo.getTransactionCode());
        recordMaterialInout.setRecordDate(DateAndTimeUtil.getNowDate());
        recordMaterialInout.setContainerCode(containerCode);
        recordMaterialInout.setRecorder(SecurityUtils.getUsername());
        recordMaterialInout.setLockTime(DateAndTimeUtil.getNowDate());
        recordMaterialInout.setUpperIndex(documentInfo.getSourceDocumentNo());
        recordMaterialInout.setBatch(detail.getBatchCode());
        recordMaterialInout.setProduceDate(new Date());
        recordMaterialInout.setUpperIndex(documentInfo.getSourceDocumentNo());
        inOutList.add(recordMaterialInout);
    }

    /**
     * 处理非立库出入
     */
    @Transactional
    public void executeWithoutLkConfirm(List<DocumentInventoryItemVo> items, Integer type) {
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            if (type == CommonConstant.InoutType.IN) {
                // 入库：创建批次库存
                String targetContainerCode = item.getContainerCode();
                BasicWarehouseContainer container = basicWarehouseContainerMapper.getContainerByCode(targetContainerCode);
                if (container != null) {
                    // 创建批次库存
                    BasicMaterialBatchInventory batchInventory = new BasicMaterialBatchInventory();
                    batchInventory.setId(UUID.randomUUID().toString());
                    // 使用DocumentInventoryDetail中的批次号
                    batchInventory.setBatch(detail.getBatchCode());
                    batchInventory.setMaterialCode(detail.getMaterialCode());
                    batchInventory.setAvailNum(item.getConfirmQuantity());
                    batchInventory.setContainerCode(targetContainerCode);
                    batchInventory.setFreezeNum(0);
                    batchInventory.setCreateTime(new Date());
                    batchInventory.setProduceDate(new Date());
                    batchInventory.setInDate(new Date());
                    batchInventory.setUpperIndex(detail.getDocumentCode());
                    batchInventory.setMaterialNum(item.getConfirmQuantity());

                    int insertResult = materialBatchInventoryMapper.insert(batchInventory);
                    if (insertResult <= 0) {
                        log.info("入库失败：批次库存创建失败，容器: {}, 批次: {}, 物料: {}",
                                targetContainerCode, detail.getBatchCode(), detail.getMaterialCode());
                        throw new CustomException("入库失败：批次库存创建失败");
                    }

                    // 更新批次记录的容器信息
                    detail.setContainerCode(targetContainerCode);
                    log.info("入库成功：创建批次库存，容器: {}, 批次: {}, 物料: {}, 数量: {}",
                            targetContainerCode, detail.getBatchCode(), detail.getMaterialCode(), item.getConfirmQuantity());
                } else {
                    log.info("入库失败：目标容器不存在，容器编码: {}", targetContainerCode);
                    throw new CustomException("入库失败：目标容器不存在");
                }
            } else {
                // 出库：扣减批次库存（根据容器、物料、批次号查询）
                BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(
                        detail.getContainerCode(), detail.getBatchCode(), detail.getMaterialCode(), null);
                if (batchInventory != null) {
                    // 验证冻结数量是否足够
                    if (batchInventory.getFreezeNum() < item.getConfirmQuantity()) {
                        log.info("出库失败：冻结数量不足，批次ID: {}, 冻结数量: {}, 需要出库数量: {}",
                                detail.getId(), batchInventory.getFreezeNum(), item.getConfirmQuantity());
                        throw new CustomException("出库失败：冻结数量不足");
                    }

                    // 验证总数量是否足够
                    if (batchInventory.getMaterialNum() < item.getConfirmQuantity()) {
                        log.info("出库失败：库存数量不足，批次ID: {}, 库存数量: {}, 需要出库数量: {}",
                                detail.getId(), batchInventory.getMaterialNum(), item.getConfirmQuantity());
                        throw new CustomException("出库失败：库存数量不足");
                    }

                    batchInventory.setFreezeNum(batchInventory.getFreezeNum() - item.getConfirmQuantity());
                    batchInventory.setMaterialNum(batchInventory.getMaterialNum() - item.getConfirmQuantity());
                    batchInventory.setAvailNum(batchInventory.getMaterialNum() - batchInventory.getFreezeNum());

                    int updateResult = materialBatchInventoryMapper.updateById(batchInventory);
                    if (updateResult <= 0) {
                        log.info("出库失败：库存更新失败，批次ID: {}", detail.getId());
                        throw new CustomException("出库失败：库存更新失败");
                    }

                    log.info("出库成功：批次ID: {}, 出库数量: {}, 剩余库存: {}",
                            detail.getId(), item.getConfirmQuantity(), batchInventory.getMaterialNum());

                    // 库存为0时删除记录
                    if (batchInventory.getMaterialNum() == 0 && batchInventory.getAvailNum() == 0) {
                        materialBatchInventoryMapper.deleteById(batchInventory.getId());
                        log.info("删除零库存记录：批次ID: {}", detail.getId());
                    }
                } else {
                    log.info("出库失败：未找到对应的批次库存，容器: {}, 批次: {}, 物料: {}",
                            detail.getContainerCode(), detail.getBatchCode(), detail.getMaterialCode());
                    throw new CustomException("出库失败：未找到对应的批次库存");
                }
            }

            // 更新批次记录
            updateInventoryDetailStatus(detail, item.getConfirmQuantity());
        }
    }

    /**
     * 处理立库出入
     */
    @Transactional
    public void executeLkConfirm(List<DocumentInventoryItemVo> items, String lkName, Integer type) {
        // 构建立库任务请求
        LkSendTaskRequest lkRequest = new LkSendTaskRequest();
        lkRequest.setId(UUID.randomUUID().toString());
        lkRequest.setTimestamp(new Date());

        List<LkSendTaskDetail> taskDetails = new ArrayList<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());

            LkSendTaskDetail taskDetail = new LkSendTaskDetail();
            taskDetail.setTaskNo(getLkTaskNo(documentDetail.getDocumentCode()));
            taskDetail.setSource("WMS");

            List<LkTaskMaterial> materials = new ArrayList<>();
            LkTaskMaterial material = new LkTaskMaterial();
            material.setMaterielCode(detail.getMaterialCode());
            material.setQuantity(item.getConfirmQuantity());
            materials.add(material);

            taskDetail.setMaterielInfos(materials);
            taskDetails.add(taskDetail);
        }
        lkRequest.setData(taskDetails);

        // 发送立库任务
        ResponseResult result;
        if (type == CommonConstant.InoutType.IN) {
            result = lkSystemService.sendLkInTask(lkRequest, lkName);
        } else {
            result = lkSystemService.sendLkOutTask(lkRequest, lkName);
        }

        // 处理成功后更新记录
        if (result != null && result.getCode().equals(ResultMsg.successCode)) {
            log.info("立库任务执行成功，立库类型: {}, 操作类型: {}, 处理数量: {}", lkName, type, items.size());
            for (DocumentInventoryItemVo item : items) {
                DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());

                // 入库时更新容器信息
                if (type == CommonConstant.InoutType.IN) {
                    detail.setContainerCode(item.getContainerCode());
                    log.info("立库入库：更新容器信息，批次ID: {}, 容器: {}", detail.getId(), item.getContainerCode());
                }

                // 更新批次记录状态
                updateInventoryDetailStatus(detail, item.getConfirmQuantity());
            }
        } else {
            String errorMsg = result != null ? result.getMsg() : "立库任务调用失败";
            log.info("立库任务执行失败，立库类型: {}, 操作类型: {}, 错误信息: {}", lkName, type, errorMsg);
            throw new CustomException("立库任务执行失败：" + errorMsg);
        }
    }

    /**
     * 更新批次记录状态
     */
    private void updateInventoryDetailStatus(DocumentInventoryDetail detail, Integer confirmQuantity) {
        detail.setCompletedNum(detail.getCompletedNum() + confirmQuantity);

        // 检查是否完全完成
        if (detail.getCompletedNum().equals(detail.getQuantity())) {
            detail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
            detail.setCompletedTime(new Date());
        }

        documentInventoryDetailService.updateById(detail);

        // 更新单据明细状态
        BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
        documentDetail.setCompletedNum(documentDetail.getCompletedNum() + confirmQuantity);
        basicDocumentDetailMapper.updateById(documentDetail);

        // 更新最新批次状态
        basicDocumentInfoService.updateLatestBatchStatus(detail.getDetailCode());
    }

    /**
     * 根据批次项更新单据状态
     */
    private void updateDocumentStatusByItems(List<DocumentInventoryItemVo> items) {
        // 获取涉及的单据ID
        Set<String> documentIds = new HashSet<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
            documentIds.add(documentDetail.getDocumentCode());
        }

        // 检查每个单据是否完成
        for (String documentId : documentIds) {
            BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentId);
            List<BasicDocumentDetail> details = basicDocumentDetailMapper.selectByDocumentCode(documentId);

            boolean allCompleted = true;
            for (BasicDocumentDetail detail : details) {
                if (!detail.getQuantity().equals(detail.getCompletedNum())) {
                    allCompleted = false;
                    break;
                }
            }

            if (allCompleted) {
                documentInfo.setStatus(CommonConstant.DocumentStatus.FINISH);
                basicDocumentInfoMapper.updateById(documentInfo);
            }
        }
    }

    /**
     * 根据批次项上报ERP
     */
    private void reportToErpByItems(List<DocumentInventoryItemVo> items, Integer type) {
        // 获取ERP来源的单据
        Set<String> erpDocumentIds = new HashSet<>();
        for (DocumentInventoryItemVo item : items) {
            DocumentInventoryDetail detail = documentInventoryDetailService.getById(item.getDocumentInventoryDetailId());
            BasicDocumentDetail documentDetail = basicDocumentDetailMapper.selectById(detail.getDetailCode());
            BasicDocumentInfo documentInfo = basicDocumentInfoMapper.selectById(documentDetail.getDocumentCode());

            if (documentInfo != null && CommonConstant.BusinessSource.ERP == documentInfo.getBusinessSource()) {
                erpDocumentIds.add(documentInfo.getId());
            }
        }

        if (!erpDocumentIds.isEmpty()) {
            // 构建DocumentDetailVo列表用于ERP上报
            List<DocumentDetailVo> erpLists = new ArrayList<>();
            for (String documentId : erpDocumentIds) {
                List<BasicDocumentDetail> details = basicDocumentDetailMapper.selectByDocumentCode(documentId);
                for (BasicDocumentDetail detail : details) {
                    DocumentDetailVo detailVo = new DocumentDetailVo();
                    detailVo.setId(detail.getId());
                    detailVo.setDocumentCode(detail.getDocumentCode());
                    detailVo.setMaterialCode(detail.getMaterialCode());
                    detailVo.setCurrentNum(detail.getCompletedNum());
                    erpLists.add(detailVo);
                }
            }
            erpReportService.reportCurrentOperation(erpLists, type);
        }
    }

    /**
     * 校验质检状态是否允许出入库
     *
     * @param qcStatus     质检状态
     * @param isConcession 是否让步接收
     * @return true-允许出入库，false-不允许出入库
     */
    private boolean isQcStatusValid(Integer qcStatus, Integer isConcession) {
        if (qcStatus == null) {
            return false;
        }

        // 允许出入库的质检状态
        if (qcStatus.equals(CommonConstant.QcStatus.NO_NEED) ||      // 无需质检
                qcStatus.equals(CommonConstant.QcStatus.QUALIFIED) ||    // 质检合格
                qcStatus.equals(CommonConstant.QcStatus.EXEMPT)) {       // 免检
            return true;
        }

        // 质检不合格但让步接收的情况
        if (qcStatus.equals(CommonConstant.QcStatus.UNQUALIFIED) &&
                isConcession != null && isConcession.equals(CommonConstant.IsConcession.YES)) {
            return true;
        }

        return false;
    }

    /**
     * 校验出入库状态是否允许操作
     *
     * @param warehouseStatus 出入库状态
     * @return true-允许操作，false-不允许操作
     */
    private boolean isWarehouseStatusValid(Integer warehouseStatus) {
        if (warehouseStatus == null) {
            return false;
        }

        // 只有待出入库状态才允许进行出入库操作
        return warehouseStatus.equals(CommonConstant.WarehouseStatus.PENDING_WAREHOUSE);
    }

    /**
     * 获取质检状态描述
     */
    private String getQcStatusDescription(Integer qcStatus) {
        if (qcStatus == null) return "未知";
        switch (qcStatus) {
            case 0:
                return "无需质检";
            case 1:
                return "待质检";
            case 2:
                return "质检中";
            case 3:
                return "质检合格";
            case 4:
                return "质检不合格";
            case 5:
                return "免检";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取出入库状态描述
     */
    private String getWarehouseStatusDescription(Integer warehouseStatus) {
        if (warehouseStatus == null) return "未知";
        switch (warehouseStatus) {
            case 0:
                return "待确认";
            case 1:
                return "待出入库";
            case 2:
                return "出入库中";
            case 3:
                return "待生产确认";
            case 4:
                return "生产已确认";
            case 5:
                return "待仓库确认";
            case 6:
                return "仓库已确认";
            case 7:
                return "已完成";
            default:
                return "未知状态";
        }
    }
}
